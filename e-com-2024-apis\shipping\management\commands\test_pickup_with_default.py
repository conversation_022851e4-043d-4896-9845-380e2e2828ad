"""
Test pickup address names with DEFAULT store name
"""

import json
import requests
from datetime import datetime
from django.core.management.base import BaseCommand
from django.conf import settings
from shipping.models import RapidshypConfiguration


class Command(BaseCommand):
    help = 'Test pickup address names with DEFAULT store name'

    def handle(self, *args, **options):
        # Pickup address names to test
        pickup_names_to_test = [
            'TRIUMPH ENTERPRISES',
            'Triumph Enterprises',
            'triumph enterprises',
            'TRIUMPH',
            'Triumph',
            'triumph',
            'DEFAULT',
            'Default',
            'default',
            'Main Warehouse',
            'Warehouse',
            'Store',
            'Office',
            'Home',
            'Primary',
            'Main',
            'Pickup',
            'Location1',
            'STORE1',
            'WAREHOUSE1'
        ]

        api_key = getattr(settings, 'RAPIDSHYP_API_KEY', None)
        base_url = getattr(settings, 'RAPIDSHYP_BASE_URL', 'https://api.rapidshyp.com/rapidshyp/apis/v1')

        # Setup session
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'rapidshyp-token': api_key,
            'User-Agent': 'Triumph-Ecommerce-Test/1.0'
        })

        self.stdout.write('Testing pickup address names with DEFAULT store...\n')

        for pickup_name in pickup_names_to_test:
            self.stdout.write(f'Testing pickup address: "{pickup_name}"')
            
            # Create test order data
            order_data = {
                "orderId": f"TEST-{datetime.now().strftime('%H%M%S')}",
                "orderDate": datetime.now().strftime('%Y-%m-%d'),
                "pickupAddressName": pickup_name,  # This is what we're testing
                "pickupLocation": {
                    "contactName": "TRIUMPH ENTERPRISES",
                    "pickupName": pickup_name,
                    "pickupEmail": "<EMAIL>",
                    "pickupPhone": "9848486452",
                    "pickupAddress1": "5-5-190/65/a",
                    "pickupAddress2": "patel nagar, NA nampally",
                    "pinCode": "500001"
                },
                "storeName": "DEFAULT",  # We know this is correct
                "billingIsShipping": True,
                "shippingAddress": {
                    "firstName": "Test",
                    "lastName": "Customer",
                    "addressLine1": "123 Test Street",
                    "addressLine2": "Test Area",
                    "pinCode": "400001",
                    "email": "<EMAIL>",
                    "phone": "9876543210"
                },
                "orderItems": [
                    {
                        "itemName": "Test Product",
                        "sku": "TEST-001",
                        "description": "Test",
                        "units": 1,
                        "unitPrice": 100.0,
                        "tax": 0.0,
                        "hsn": "",
                        "productLength": 10.0,
                        "productBreadth": 10.0,
                        "productHeight": 5.0,
                        "productWeight": 500,
                        "brand": "",
                        "imageURL": "",
                        "isFragile": False,
                        "isPersonalisable": False
                    }
                ],
                "paymentMethod": "PREPAID",
                "shippingCharges": 0.0,
                "totalOrderValue": 100.0,
                "packageDetails": {
                    "packageLength": 10.0,
                    "packageBreadth": 10.0,
                    "packageHeight": 10.0,
                    "packageWeight": 500
                }
            }

            try:
                response = session.post(f"{base_url}/wrapper", json=order_data, timeout=30)
                
                if response.content:
                    try:
                        json_data = response.json()
                        status = json_data.get('status', 'UNKNOWN')
                        remarks = json_data.get('remarks', 'No remarks')
                        
                        if status == 'SUCCESS' or json_data.get('orderCreated', False):
                            self.stdout.write(self.style.SUCCESS(f'  ✓ SUCCESS: {pickup_name}'))
                            self.stdout.write(f'    Order ID: {json_data.get("orderId", "N/A")}')
                            
                            # Update configuration with working pickup name
                            config = RapidshypConfiguration.objects.filter(is_active=True).first()
                            if config:
                                config.pickup_address_name = pickup_name
                                config.save()
                                self.stdout.write(f'    Updated configuration with pickup name: {pickup_name}')
                            return  # Found working pickup name
                        else:
                            if 'Pickup address not found' in remarks:
                                self.stdout.write(self.style.ERROR(f'  ✗ Pickup address not found'))
                            else:
                                self.stdout.write(self.style.WARNING(f'  ✗ Different error: {remarks}'))
                            
                    except json.JSONDecodeError:
                        self.stdout.write(self.style.ERROR(f'  ✗ Invalid JSON response'))
                else:
                    self.stdout.write(self.style.ERROR(f'  ✗ Empty response'))

            except Exception as e:
                self.stdout.write(self.style.ERROR(f'  ✗ Request failed: {e}'))

        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.WARNING('No valid pickup address name found!'))
        self.stdout.write('\nYou need to create a pickup location in your Rapidshyp dashboard first.')
        self.stdout.write('Please:')
        self.stdout.write('1. Log into your Rapidshyp dashboard')
        self.stdout.write('2. Go to Pickup Locations or similar section')
        self.stdout.write('3. Create a new pickup location with your warehouse details')
        self.stdout.write('4. Note the exact name you give to the pickup location')
        self.stdout.write('5. Update the configuration with that exact name')
