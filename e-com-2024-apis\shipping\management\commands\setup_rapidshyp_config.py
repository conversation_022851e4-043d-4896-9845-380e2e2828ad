"""
Django management command to set up Rapidshyp configuration
"""

from django.core.management.base import BaseCommand, CommandError
from shipping.models import RapidshypConfiguration


class Command(BaseCommand):
    help = 'Set up Rapidshyp configuration for testing and production'

    def add_arguments(self, parser):
        parser.add_argument(
            '--store-name',
            type=str,
            default='DEFAULT',
            help='Store name for Rapidshyp configuration',
        )
        parser.add_argument(
            '--pickup-address-name',
            type=str,
            default='Main Warehouse',
            help='Pickup address name',
        )
        parser.add_argument(
            '--pickup-pincode',
            type=str,
            default='110001',
            help='Default pickup pincode (6 digits)',
        )
        parser.add_argument(
            '--contact-name',
            type=str,
            default='Store Manager',
            help='Contact person name',
        )
        parser.add_argument(
            '--contact-phone',
            type=str,
            default='9876543210',
            help='Contact phone number',
        )
        parser.add_argument(
            '--contact-email',
            type=str,
            default='<EMAIL>',
            help='Contact email address',
        )
        parser.add_argument(
            '--address-line-1',
            type=str,
            default='123 Business Street',
            help='Address line 1',
        )
        parser.add_argument(
            '--address-line-2',
            type=str,
            default='Business District',
            help='Address line 2 (optional)',
        )
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing configuration if it exists',
        )

    def handle(self, *args, **options):
        # Validate pincode
        pickup_pincode = options['pickup_pincode']
        if not pickup_pincode.isdigit() or len(pickup_pincode) != 6:
            raise CommandError('Pickup pincode must be exactly 6 digits')

        # Validate phone number
        contact_phone = options['contact_phone']
        if not contact_phone.isdigit() or len(contact_phone) != 10:
            raise CommandError('Contact phone must be exactly 10 digits')

        # Check if configuration already exists
        existing_config = RapidshypConfiguration.objects.filter(
            store_name=options['store_name']
        ).first()

        if existing_config and not options['update']:
            self.stdout.write(
                self.style.WARNING(
                    f'Configuration for store "{options["store_name"]}" already exists. '
                    'Use --update flag to update it.'
                )
            )
            self.stdout.write(f'Existing configuration:')
            self.stdout.write(f'  Store Name: {existing_config.store_name}')
            self.stdout.write(f'  Pickup Address: {existing_config.pickup_address_name}')
            self.stdout.write(f'  Pickup Pincode: {existing_config.default_pickup_pincode}')
            self.stdout.write(f'  Contact: {existing_config.contact_name} ({existing_config.contact_phone})')
            self.stdout.write(f'  Email: {existing_config.contact_email}')
            self.stdout.write(f'  Address: {existing_config.address_line_1}')
            if existing_config.address_line_2:
                self.stdout.write(f'           {existing_config.address_line_2}')
            self.stdout.write(f'  Active: {existing_config.is_active}')
            return

        # Create or update configuration
        config_data = {
            'store_name': options['store_name'],
            'pickup_address_name': options['pickup_address_name'],
            'default_pickup_pincode': pickup_pincode,
            'contact_name': options['contact_name'],
            'contact_phone': contact_phone,
            'contact_email': options['contact_email'],
            'address_line_1': options['address_line_1'],
            'address_line_2': options['address_line_2'],
            'is_active': True
        }

        if existing_config and options['update']:
            # Update existing configuration
            for key, value in config_data.items():
                setattr(existing_config, key, value)
            existing_config.save()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated Rapidshyp configuration for store "{options["store_name"]}"'
                )
            )
            config = existing_config
        else:
            # Create new configuration
            config = RapidshypConfiguration.objects.create(**config_data)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created Rapidshyp configuration for store "{options["store_name"]}"'
                )
            )

        # Display configuration details
        self.stdout.write('\nConfiguration Details:')
        self.stdout.write(f'  Store Name: {config.store_name}')
        self.stdout.write(f'  Pickup Address: {config.pickup_address_name}')
        self.stdout.write(f'  Pickup Pincode: {config.default_pickup_pincode}')
        self.stdout.write(f'  Contact: {config.contact_name} ({config.contact_phone})')
        self.stdout.write(f'  Email: {config.contact_email}')
        self.stdout.write(f'  Address: {config.address_line_1}')
        if config.address_line_2:
            self.stdout.write(f'           {config.address_line_2}')
        self.stdout.write(f'  Active: {config.is_active}')
        self.stdout.write(f'  Created: {config.created_at}')

        # Provide next steps
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('Next Steps:'))
        self.stdout.write('1. Ensure RAPIDSHYP_API_KEY is set in your environment variables')
        self.stdout.write('2. Ensure RAPIDSHYP_ENABLED=True in your settings')
        self.stdout.write('3. Test the configuration with: python manage.py test_rapidshyp_connection')
        self.stdout.write('4. Try creating a Rapidshyp order from the frontend')
