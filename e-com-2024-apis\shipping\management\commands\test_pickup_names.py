"""
Test different pickup address names with Rapidshyp API
"""

import json
import requests
from datetime import datetime
from django.core.management.base import BaseCommand
from django.conf import settings
from shipping.models import RapidshypConfiguration


class Command(BaseCommand):
    help = 'Test different pickup address names with Rapidshyp API'

    def handle(self, *args, **options):
        # Get current configuration
        config = RapidshypConfiguration.objects.filter(is_active=True).first()
        if not config:
            self.stdout.write(self.style.ERROR('No Rapidshyp configuration found'))
            return

        # Pickup address names to test
        pickup_names_to_test = [
            'Main Warehouse',
            'DEFAULT',
            'Home',
            'Warehouse',
            'Store',
            'Office',
            'Pickup Location',
            'Primary',
            'Main',
            'Default Pickup',
            'Triumph Warehouse',
            'Triumph Store'
        ]

        api_key = getattr(settings, 'RAPIDSHYP_API_KEY', None)
        base_url = getattr(settings, 'RAPIDSHYP_BASE_URL', 'https://api.rapidshyp.com/rapidshyp/apis/v1')

        # Setup session
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'rapidshyp-token': api_key,
            'User-Agent': 'Triumph-Ecommerce-Test/1.0'
        })

        self.stdout.write('Testing different pickup address names...\n')

        for pickup_name in pickup_names_to_test:
            self.stdout.write(f'Testing pickup address name: "{pickup_name}"')
            
            # Create test order data
            order_data = {
                "orderId": f"TEST-PICKUP-{datetime.now().strftime('%H%M%S')}",
                "orderDate": datetime.now().strftime('%Y-%m-%d'),
                "pickupAddressName": pickup_name,  # This is what we're testing
                "pickupLocation": {
                    "contactName": config.contact_name,
                    "pickupName": pickup_name,
                    "pickupEmail": config.contact_email,
                    "pickupPhone": config.contact_phone,
                    "pickupAddress1": config.address_line_1,
                    "pickupAddress2": config.address_line_2,
                    "pinCode": config.default_pickup_pincode
                },
                "storeName": "DEFAULT",  # We know this is correct
                "billingIsShipping": True,
                "shippingAddress": {
                    "firstName": "Test",
                    "lastName": "Customer",
                    "addressLine1": "123 Test Street",
                    "addressLine2": "Test Area",
                    "pinCode": "400001",
                    "email": "<EMAIL>",
                    "phone": "9876543210"
                },
                "orderItems": [
                    {
                        "itemName": "Test Product",
                        "sku": "TEST-SKU-001",
                        "description": "Test Product Description",
                        "units": 1,
                        "unitPrice": 100.0,
                        "tax": 0.0,
                        "hsn": "",
                        "productLength": 10.0,
                        "productBreadth": 10.0,
                        "productHeight": 5.0,
                        "productWeight": 500,
                        "brand": "",
                        "imageURL": "",
                        "isFragile": False,
                        "isPersonalisable": False
                    }
                ],
                "paymentMethod": "PREPAID",
                "shippingCharges": 0.0,
                "totalOrderValue": 100.0,
                "packageDetails": {
                    "packageLength": 10.0,
                    "packageBreadth": 10.0,
                    "packageHeight": 10.0,
                    "packageWeight": 500
                }
            }

            try:
                response = session.post(f"{base_url}/wrapper", json=order_data, timeout=30)
                
                if response.content:
                    try:
                        json_data = response.json()
                        status = json_data.get('status', 'UNKNOWN')
                        remarks = json_data.get('remarks', 'No remarks')
                        
                        if status == 'SUCCESS' or json_data.get('orderCreated', False):
                            self.stdout.write(self.style.SUCCESS(f'  ✓ SUCCESS: {pickup_name}'))
                            self.stdout.write(f'    Order ID: {json_data.get("orderId", "N/A")}')
                            
                            # Update configuration with working pickup name
                            config.pickup_address_name = pickup_name
                            config.save()
                            self.stdout.write(f'    Updated configuration with pickup name: {pickup_name}')
                            break  # Found working pickup name
                        else:
                            self.stdout.write(self.style.WARNING(f'  ✗ FAILED: {remarks}'))
                            
                    except json.JSONDecodeError:
                        self.stdout.write(self.style.ERROR(f'  ✗ Invalid JSON response'))
                else:
                    self.stdout.write(self.style.ERROR(f'  ✗ Empty response'))

            except Exception as e:
                self.stdout.write(self.style.ERROR(f'  ✗ Request failed: {e}'))

        self.stdout.write('\nTest completed.')
        self.stdout.write('If a working pickup name was found, try creating an order from the frontend.')
        self.stdout.write('If no pickup name worked, you may need to create a pickup location in your Rapidshyp dashboard first.')
