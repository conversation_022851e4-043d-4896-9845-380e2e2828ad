"""
Test more store name variations with Rapidshyp API
"""

import json
import requests
from datetime import datetime
from django.core.management.base import BaseCommand
from django.conf import settings
from shipping.models import RapidshypConfiguration


class Command(BaseCommand):
    help = 'Test more store name variations with Rapidshyp API'

    def handle(self, *args, **options):
        # Get current configuration
        config = RapidshypConfiguration.objects.filter(is_active=True).first()
        if not config:
            self.stdout.write(self.style.ERROR('No Rapidshyp configuration found'))
            return

        # More store names to test based on dashboard info
        store_names_to_test = [
            'DEFAULT',
            'TRIUMPH ENTERPRISES',
            '25006339',  # Seller code
            'TRIUMPH',
            'Triumph',
            'triumph',
            'Store1',
            'Main',
            'Primary',
            'Channel1',
            'Default Store',
            'MAIN_STORE',
            'triumph_enterprises',
            'TRIUMPH_ENTERPRISES',
            'Store',
            'STORE'
        ]

        api_key = getattr(settings, 'RAPIDSHYP_API_KEY', None)
        base_url = getattr(settings, 'RAPIDSHYP_BASE_URL', 'https://api.rapidshyp.com/rapidshyp/apis/v1')

        # Setup session
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'rapidshyp-token': api_key,
            'User-Agent': 'Triumph-Ecommerce-Test/1.0'
        })

        self.stdout.write('Testing more store name variations...\n')

        for store_name in store_names_to_test:
            self.stdout.write(f'Testing store name: "{store_name}"')
            
            # Create minimal test order data
            order_data = {
                "orderId": f"TEST-{datetime.now().strftime('%H%M%S')}",
                "orderDate": datetime.now().strftime('%Y-%m-%d'),
                "pickupAddressName": "TRIUMPH ENTERPRISES",
                "pickupLocation": {
                    "contactName": "TRIUMPH ENTERPRISES",
                    "pickupName": "TRIUMPH ENTERPRISES",
                    "pickupEmail": "<EMAIL>",
                    "pickupPhone": "9848486452",
                    "pickupAddress1": "5-5-190/65/a",
                    "pickupAddress2": "patel nagar, NA nampally",
                    "pinCode": "500001"
                },
                "storeName": store_name,  # This is what we're testing
                "billingIsShipping": True,
                "shippingAddress": {
                    "firstName": "Test",
                    "lastName": "Customer",
                    "addressLine1": "123 Test Street",
                    "addressLine2": "Test Area",
                    "pinCode": "400001",
                    "email": "<EMAIL>",
                    "phone": "9876543210"
                },
                "orderItems": [
                    {
                        "itemName": "Test Product",
                        "sku": "TEST-001",
                        "description": "Test",
                        "units": 1,
                        "unitPrice": 100.0,
                        "tax": 0.0,
                        "hsn": "",
                        "productLength": 10.0,
                        "productBreadth": 10.0,
                        "productHeight": 5.0,
                        "productWeight": 500,
                        "brand": "",
                        "imageURL": "",
                        "isFragile": False,
                        "isPersonalisable": False
                    }
                ],
                "paymentMethod": "PREPAID",
                "shippingCharges": 0.0,
                "totalOrderValue": 100.0,
                "packageDetails": {
                    "packageLength": 10.0,
                    "packageBreadth": 10.0,
                    "packageHeight": 10.0,
                    "packageWeight": 500
                }
            }

            try:
                response = session.post(f"{base_url}/wrapper", json=order_data, timeout=30)
                
                if response.content:
                    try:
                        json_data = response.json()
                        status = json_data.get('status', 'UNKNOWN')
                        remarks = json_data.get('remarks', 'No remarks')
                        
                        if status == 'SUCCESS' or json_data.get('orderCreated', False):
                            self.stdout.write(self.style.SUCCESS(f'  ✓ SUCCESS: {store_name}'))
                            self.stdout.write(f'    Order ID: {json_data.get("orderId", "N/A")}')
                            
                            # Update configuration with working store name
                            config.store_name = store_name
                            config.save()
                            self.stdout.write(f'    Updated configuration with store name: {store_name}')
                            return  # Found working store name
                        else:
                            if 'Invalid store name' in remarks:
                                self.stdout.write(self.style.ERROR(f'  ✗ Invalid store name'))
                            else:
                                self.stdout.write(self.style.WARNING(f'  ✗ Different error: {remarks}'))
                            
                    except json.JSONDecodeError:
                        self.stdout.write(self.style.ERROR(f'  ✗ Invalid JSON response'))
                else:
                    self.stdout.write(self.style.ERROR(f'  ✗ Empty response'))

            except Exception as e:
                self.stdout.write(self.style.ERROR(f'  ✗ Request failed: {e}'))

        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.WARNING('No valid store name found!'))
        self.stdout.write('\nPlease check your Rapidshyp dashboard for:')
        self.stdout.write('1. Store/Channel management section')
        self.stdout.write('2. Available store names in order creation')
        self.stdout.write('3. Contact Rapidshyp support for the correct store name')
        self.stdout.write('\nThe store name must exactly match what\'s configured in your Rapidshyp account.')
