"""
Django management command to test Rapidshyp API connection and configuration
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from shipping.models import RapidshypConfiguration
from shipping.services import ShippingService


class Command(BaseCommand):
    help = 'Test Rapidshyp API connection and configuration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-pincode',
            type=str,
            default='110001',
            help='Test delivery pincode for serviceability check',
        )
        parser.add_argument(
            '--test-weight',
            type=float,
            default=1.0,
            help='Test package weight in kg',
        )

    def handle(self, *args, **options):
        self.stdout.write('Testing Rapidshyp Integration...\n')

        # 1. Check environment variables
        self.stdout.write('1. Checking Environment Variables:')
        
        rapidshyp_enabled = getattr(settings, 'RAPIDSHYP_ENABLED', False)
        rapidshyp_api_key = getattr(settings, 'RAPIDSHYP_API_KEY', None)
        rapidshyp_base_url = getattr(settings, 'RAPIDSHYP_BASE_URL', None)
        
        if rapidshyp_enabled:
            self.stdout.write(self.style.SUCCESS('   ✓ RAPIDSHYP_ENABLED = True'))
        else:
            self.stdout.write(self.style.ERROR('   ✗ RAPIDSHYP_ENABLED = False'))
            
        if rapidshyp_api_key:
            masked_key = rapidshyp_api_key[:8] + '*' * (len(rapidshyp_api_key) - 8)
            self.stdout.write(self.style.SUCCESS(f'   ✓ RAPIDSHYP_API_KEY = {masked_key}'))
        else:
            self.stdout.write(self.style.ERROR('   ✗ RAPIDSHYP_API_KEY not set'))
            
        if rapidshyp_base_url:
            self.stdout.write(self.style.SUCCESS(f'   ✓ RAPIDSHYP_BASE_URL = {rapidshyp_base_url}'))
        else:
            self.stdout.write(self.style.WARNING('   ! RAPIDSHYP_BASE_URL not set (using default)'))

        # 2. Check database configuration
        self.stdout.write('\n2. Checking Database Configuration:')
        
        try:
            config = RapidshypConfiguration.objects.filter(is_active=True).first()
            if config:
                self.stdout.write(self.style.SUCCESS('   ✓ Rapidshyp configuration found'))
                self.stdout.write(f'     Store: {config.store_name}')
                self.stdout.write(f'     Pickup: {config.pickup_address_name} ({config.default_pickup_pincode})')
                self.stdout.write(f'     Contact: {config.contact_name} ({config.contact_phone})')
            else:
                self.stdout.write(self.style.ERROR('   ✗ No active Rapidshyp configuration found'))
                self.stdout.write('     Run: python manage.py setup_rapidshyp_config')
                return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ✗ Database error: {e}'))
            return

        # 3. Test shipping service initialization
        self.stdout.write('\n3. Testing Shipping Service:')
        
        try:
            shipping_service = ShippingService()
            if shipping_service.rapidshyp_enabled:
                self.stdout.write(self.style.SUCCESS('   ✓ ShippingService initialized with Rapidshyp enabled'))
            else:
                self.stdout.write(self.style.ERROR('   ✗ ShippingService initialized but Rapidshyp disabled'))
                return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ✗ ShippingService initialization failed: {e}'))
            return

        # 4. Test API connectivity
        self.stdout.write('\n4. Testing API Connectivity:')
        
        try:
            is_available = shipping_service.is_rapidshyp_available()
            if is_available:
                self.stdout.write(self.style.SUCCESS('   ✓ Rapidshyp API is reachable'))
            else:
                self.stdout.write(self.style.ERROR('   ✗ Rapidshyp API is not reachable'))
                return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ✗ API connectivity test failed: {e}'))
            return

        # 5. Test serviceability check
        self.stdout.write('\n5. Testing Serviceability Check:')
        
        try:
            test_pincode = options['test_pincode']
            test_weight = options['test_weight']
            
            self.stdout.write(f'   Testing delivery to pincode: {test_pincode}')
            self.stdout.write(f'   Package weight: {test_weight} kg')
            
            rates = shipping_service.get_shipping_rates(
                pickup_pincode=config.default_pickup_pincode,
                delivery_pincode=test_pincode,
                weight=test_weight,
                cod=False,
                total_value=1000
            )
            
            if rates.get('success') and rates.get('rates'):
                self.stdout.write(self.style.SUCCESS(f'   ✓ Found {len(rates["rates"])} shipping options'))
                
                # Show first few rates
                for i, rate in enumerate(rates['rates'][:3]):
                    self.stdout.write(f'     {i+1}. {rate["courier_name"]} - ₹{rate["total_freight"]} ({rate["estimated_days"]} days)')
                    
                if len(rates['rates']) > 3:
                    self.stdout.write(f'     ... and {len(rates["rates"]) - 3} more options')
                    
            else:
                error_msg = rates.get('error', 'Unknown error')
                self.stdout.write(self.style.ERROR(f'   ✗ Serviceability check failed: {error_msg}'))
                return
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ✗ Serviceability test failed: {e}'))
            return

        # 6. Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('🎉 All tests passed! Rapidshyp integration is ready.'))
        self.stdout.write('\nYou can now:')
        self.stdout.write('• Create Rapidshyp orders from the frontend')
        self.stdout.write('• Track shipments in real-time')
        self.stdout.write('• Use the shipping rate calculator')
        self.stdout.write('\nFor webhook setup, configure:')
        self.stdout.write(f'• Webhook URL: https://your-domain.com/api/v1/shipping/webhook/')
        self.stdout.write('• Webhook Secret: Set RAPIDSHYP_WEBHOOK_SECRET in environment')
        
        # Show service status
        status = shipping_service.get_service_status()
        self.stdout.write(f'\nService Status:')
        self.stdout.write(f'• Rapidshyp Enabled: {status["rapidshyp_enabled"]}')
        self.stdout.write(f'• Rapidshyp Available: {status["rapidshyp_available"]}')
        self.stdout.write(f'• Service Mode: {status["service_mode"]}')
        self.stdout.write(f'• Fallback Methods: {status["existing_methods_count"]}')
