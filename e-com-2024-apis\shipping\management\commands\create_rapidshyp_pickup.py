"""
Create pickup location in Rapidshyp
"""

import json
import requests
from django.core.management.base import BaseCommand
from django.conf import settings
from shipping.models import RapidshypConfiguration


class Command(BaseCommand):
    help = 'Create pickup location in Rapidshyp'

    def add_arguments(self, parser):
        parser.add_argument(
            '--address-name',
            type=str,
            default='Main Warehouse',
            help='Pickup address name',
        )

    def handle(self, *args, **options):
        # Get current configuration
        config = RapidshypConfiguration.objects.filter(is_active=True).first()
        if not config:
            self.stdout.write(self.style.ERROR('No Rapidshyp configuration found'))
            return

        api_key = getattr(settings, 'RAPIDSHYP_API_KEY', None)
        base_url = getattr(settings, 'RAPIDSHYP_BASE_URL', 'https://api.rapidshyp.com/rapidshyp/apis/v1')

        if not api_key:
            self.stdout.write(self.style.ERROR('RAPIDSHYP_API_KEY not configured'))
            return

        # Setup session
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'rapidshyp-token': api_key,
            'User-Agent': 'Triumph-Ecommerce/1.0'
        })

        # Pickup location data
        pickup_data = {
            "address_name": options['address_name'],
            "contact_name": config.contact_name,
            "contact_number": config.contact_phone,
            "email": config.contact_email,
            "address_line": config.address_line_1,
            "pincode": config.default_pickup_pincode,
            "use_alt_rto_address": False,
            "dropship_location": False  # Added missing field
        }

        self.stdout.write(f'Creating pickup location: {options["address_name"]}')
        self.stdout.write(f'Payload: {json.dumps(pickup_data, indent=2)}')

        try:
            response = session.post(f"{base_url}/create/pickup_location", json=pickup_data, timeout=30)
            
            self.stdout.write(f'Status Code: {response.status_code}')
            
            if response.content:
                try:
                    json_data = response.json()
                    self.stdout.write(f'Response: {json.dumps(json_data, indent=2)}')
                    
                    if response.status_code == 200 and json_data.get('status') == 'SUCCESS':
                        self.stdout.write(self.style.SUCCESS('✓ Pickup location created successfully!'))
                        
                        # Update configuration with the correct address name
                        config.pickup_address_name = options['address_name']
                        config.save()
                        
                        self.stdout.write(f'Updated configuration with pickup address name: {options["address_name"]}')
                        
                    else:
                        error_msg = json_data.get('message', json_data.get('remarks', 'Unknown error'))
                        self.stdout.write(self.style.ERROR(f'✗ Failed to create pickup location: {error_msg}'))
                        
                except json.JSONDecodeError:
                    self.stdout.write(self.style.ERROR(f'✗ Invalid JSON response: {response.text}'))
            else:
                self.stdout.write(self.style.ERROR('✗ Empty response'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Request failed: {e}'))

        self.stdout.write('\nNext steps:')
        self.stdout.write('1. If pickup location was created successfully, try creating a Rapidshyp order again')
        self.stdout.write('2. Use: python manage.py test_rapidshyp_order')
        self.stdout.write('3. Or create an order from the frontend')
